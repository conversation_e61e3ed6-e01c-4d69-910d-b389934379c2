"""
Core module for fundamental system components.

This module contains the core components that form the foundation
of the Actor-based system architecture.
"""

from .actor import Actor, ActorState
from .actor_system import ActorSystem, ActorSystemState
from .event_bus import event_bus
from .message_context import MessageContext, MessageContextManager

__all__ = [
    "Actor",
    "ActorState", 
    "ActorSystem",
    "ActorSystemState",
    "event_bus",
    "MessageContext",
    "MessageContextManager"
]
