#!/usr/bin/env python3
"""
Zeitwahl AI Agent - Main Application Entry Point

This module orchestrates the entire Actor-based system, initializing the ActorSystem
and managing the lifecycle of all actors in the application.
"""

import asyncio
import logging
import sys

from app.config import settings
from app.utils.actor_system import ActorSystem


class ZeitwählApp:
    """Main application class that orchestrates the Actor-based system."""

    def __init__(self):
        self.actor_system: ActorSystem = None
        self.running = False

        # Setup logging
        self._setup_logging()
    
    def _setup_logging(self):
        """Configure logging for the application."""
        log_level = getattr(logging, settings.app.log_level.upper(), logging.INFO)

        logging.basicConfig(
            level=log_level,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.StreamHandler(sys.stdout),
                # In production, you might want to add file handlers
            ]
        )

        # Set specific log levels for noisy libraries
        logging.getLogger('aiogram').setLevel(logging.WARNING)
        logging.getLogger('httpx').setLevel(logging.WARNING)

        logger = logging.getLogger(__name__)
        logger.info(f"Logging configured at {settings.app.log_level} level")

    async def initialize(self):
        """Initialize the Actor-based system."""
        logger = logging.getLogger(__name__)
        logger.info("Initializing Zeitwahl AI Agent with Actor-based architecture...")

        try:
            # Validate configuration
            await self._validate_configuration()

            # Initialize the Actor System
            logger.info("Initializing Actor System...")
            self.actor_system = ActorSystem()

            logger.info("Actor System initialized successfully")

        except Exception as e:
            logger.error(f"Failed to initialize application: {e}")
            raise
    
    async def _validate_configuration(self):
        """Validate critical configuration settings."""
        logger = logging.getLogger(__name__)

        # Check required settings
        if not settings.telegram.bot_token:
            raise ValueError("TELEGRAM_BOT_TOKEN is required")

        # Warn about missing optional settings
        if not settings.llm.gemini_api_key and not settings.llm.deepseek_api_key:
            logger.warning("No LLM API keys configured, using mock provider only")

        if not settings.calendar.google_client_id and not settings.calendar.outlook_client_id:
            logger.warning("No calendar integrations configured, using mock provider only")

        logger.info("Configuration validation completed")

    async def start(self):
        """Start the Actor-based application."""
        logger = logging.getLogger(__name__)

        if self.running:
            logger.warning("Application is already running")
            return

        try:
            logger.info("Starting Zeitwahl AI Agent with Actor System...")
            self.running = True

            # Start the Actor System (this will start all actors)
            await self.actor_system.start()

        except Exception as e:
            logger.error(f"Failed to start application: {e}")
            await self.shutdown()
            raise

    async def shutdown(self):
        """Gracefully shutdown the Actor-based application."""
        logger = logging.getLogger(__name__)

        if not self.running:
            return

        logger.info("Shutting down Zeitwahl AI Agent...")
        self.running = False

        try:
            # Stop the Actor System (this will stop all actors)
            if self.actor_system:
                await self.actor_system.stop()

            logger.info("Shutdown completed successfully")

        except Exception as e:
            logger.error(f"Error during shutdown: {e}")
    
    async def health_check(self) -> dict:
        """Perform application health check."""
        health_status = {
            "status": "healthy",
            "timestamp": asyncio.get_event_loop().time(),
            "components": {}
        }

        try:
            # Check Actor System
            if self.actor_system:
                if self.actor_system.is_healthy():
                    health_status["components"]["actor_system"] = "healthy"
                    health_status.update(self.actor_system.get_system_status())
                else:
                    health_status["components"]["actor_system"] = "degraded"
                    health_status["status"] = "degraded"
                    health_status.update(self.actor_system.get_system_status())
            else:
                health_status["components"]["actor_system"] = "not_initialized"
                health_status["status"] = "degraded"

        except Exception as e:
            health_status["status"] = "unhealthy"
            health_status["error"] = str(e)

        return health_status


async def main():
    """Main entry point for the application."""
    logger = logging.getLogger(__name__)
    
    try:
        # Create and initialize the application
        app = ZeitwählApp()
        await app.initialize()
        
        # Start the application
        logger.info("🚀 Zeitwahl AI Agent is starting...")
        logger.info(f"Environment: {settings.app.environment}")
        logger.info(f"Debug mode: {settings.app.debug}")
        
        await app.start()
        
    except KeyboardInterrupt:
        logger.info("Received keyboard interrupt")
    except Exception as e:
        logger.error(f"Application failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    # Run the application
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\nGracefully shutting down...")
    except Exception as e:
        print(f"Fatal error: {e}")
        sys.exit(1)
