"""
Validation Service for message validation and pre-filtering.

This service validates incoming messages and applies pre-filters to reduce
load on the LLM by handling simple cases and irrelevant messages.
"""

import logging
import re
from typing import Dict, Any, Optional, List, Tuple

logger = logging.getLogger(__name__)


class ValidationService:
    """Service for validating and pre-filtering messages."""
    
    def __init__(self):
        # Keywords that indicate scheduling/time-related content
        self.scheduling_keywords = {
            "schedule", "calendar", "meeting", "appointment", "time", "date",
            "when", "tomorrow", "today", "yesterday", "next week", "last week",
            "monday", "tuesday", "wednesday", "thursday", "friday", "saturday", "sunday",
            "january", "february", "march", "april", "may", "june",
            "july", "august", "september", "october", "november", "december",
            "book", "reserve", "plan", "organize", "remind", "reminder",
            "timezone", "clock", "hour", "minute", "am", "pm",
            "deadline", "due", "event", "busy", "free", "available",
            "reschedule", "cancel", "postpone", "move", "shift"
        }
        
        # Keywords that indicate irrelevant content
        self.irrelevant_keywords = {
            "weather", "joke", "funny", "meme", "game", "play",
            "music", "song", "movie", "film", "tv", "show",
            "food", "recipe", "cook", "restaurant", "eat",
            "sports", "football", "basketball", "soccer", "tennis",
            "politics", "news", "celebrity", "gossip",
            "shopping", "buy", "purchase", "price", "cost",
            "travel", "vacation", "holiday", "trip" # Note: travel can be scheduling-related
        }
        
        # Common greetings and simple responses
        self.simple_patterns = {
            "greeting": [
                r"^(hi|hello|hey|good morning|good afternoon|good evening)!?$",
                r"^(how are you|what's up|sup)[\?!]*$"
            ],
            "thanks": [
                r"^(thanks|thank you|thx|ty)!?$",
                r"^(ok|okay|alright|got it)!?$"
            ],
            "goodbye": [
                r"^(bye|goodbye|see you|cya|later)!?$",
                r"^(good night|goodnight)!?$"
            ]
        }
    
    async def validate_and_classify_message(self, message_text: str) -> Dict[str, Any]:
        """
        Validate and classify a message to determine how it should be processed.
        
        Args:
            message_text: The message text to validate
            
        Returns:
            Dictionary containing validation results and classification
        """
        try:
            # Clean and normalize the message
            cleaned_message = self._clean_message(message_text)
            
            # Check if message is empty or too short
            if len(cleaned_message.strip()) < 2:
                return {
                    "is_valid": False,
                    "classification": "empty",
                    "needs_llm": False,
                    "suggested_response": "I didn't receive a clear message. How can I help you with scheduling or time management?",
                    "confidence": 1.0
                }
            
            # Check for simple patterns that don't need LLM
            simple_response = self._check_simple_patterns(cleaned_message)
            if simple_response:
                return {
                    "is_valid": True,
                    "classification": simple_response["type"],
                    "needs_llm": False,
                    "suggested_response": simple_response["response"],
                    "confidence": simple_response["confidence"]
                }
            
            # Check relevance to scheduling/time management
            relevance_score = self._calculate_relevance_score(cleaned_message)
            
            if relevance_score < 0.3:
                return {
                    "is_valid": True,
                    "classification": "irrelevant",
                    "needs_llm": False,
                    "suggested_response": self._get_irrelevant_response(),
                    "confidence": 1.0 - relevance_score,
                    "relevance_score": relevance_score
                }
            
            # Message seems relevant, needs LLM processing
            return {
                "is_valid": True,
                "classification": "relevant",
                "needs_llm": True,
                "suggested_response": None,
                "confidence": relevance_score,
                "relevance_score": relevance_score
            }
            
        except Exception as e:
            logger.error(f"Error validating message: {e}")
            return {
                "is_valid": True,
                "classification": "error",
                "needs_llm": True,
                "suggested_response": None,
                "confidence": 0.0,
                "error": str(e)
            }
    
    def _clean_message(self, message_text: str) -> str:
        """Clean and normalize the message text."""
        # Remove extra whitespace
        cleaned = re.sub(r'\s+', ' ', message_text.strip())
        
        # Convert to lowercase for analysis
        return cleaned.lower()
    
    def _check_simple_patterns(self, message: str) -> Optional[Dict[str, Any]]:
        """Check if the message matches simple patterns that don't need LLM."""
        for pattern_type, patterns in self.simple_patterns.items():
            for pattern in patterns:
                if re.match(pattern, message, re.IGNORECASE):
                    response = self._get_simple_response(pattern_type)
                    return {
                        "type": pattern_type,
                        "response": response,
                        "confidence": 0.9
                    }
        return None
    
    def _get_simple_response(self, pattern_type: str) -> str:
        """Get appropriate response for simple patterns."""
        responses = {
            "greeting": "Hello! I'm Zeitwahl, your AI scheduling assistant. How can I help you with your calendar or time management today?",
            "thanks": "You're welcome! Is there anything else I can help you with regarding scheduling or time management?",
            "goodbye": "Goodbye! Feel free to reach out anytime you need help with scheduling or time management."
        }
        return responses.get(pattern_type, "How can I help you with scheduling or time management?")
    
    def _calculate_relevance_score(self, message: str) -> float:
        """Calculate how relevant the message is to scheduling/time management."""
        words = message.split()
        total_words = len(words)
        
        if total_words == 0:
            return 0.0
        
        # Count scheduling-related keywords
        scheduling_matches = sum(1 for word in words if any(keyword in word for keyword in self.scheduling_keywords))
        
        # Count irrelevant keywords (negative score)
        irrelevant_matches = sum(1 for word in words if any(keyword in word for keyword in self.irrelevant_keywords))
        
        # Check for time patterns (dates, times, etc.)
        time_patterns = self._count_time_patterns(message)
        
        # Calculate base relevance score
        relevance_score = (scheduling_matches + time_patterns * 2) / total_words
        
        # Subtract irrelevant content penalty
        irrelevant_penalty = irrelevant_matches / total_words
        relevance_score -= irrelevant_penalty * 0.5
        
        # Boost score for certain high-confidence patterns
        if any(phrase in message for phrase in ["schedule a", "book a", "set up a", "plan a", "when is", "what time"]):
            relevance_score += 0.3
        
        # Ensure score is between 0 and 1
        return max(0.0, min(1.0, relevance_score))
    
    def _count_time_patterns(self, message: str) -> int:
        """Count time-related patterns in the message."""
        time_patterns = [
            r'\d{1,2}:\d{2}',  # Time format (e.g., 14:30, 2:30)
            r'\d{1,2}(am|pm)',  # Time with am/pm
            r'\d{1,2}/\d{1,2}(/\d{2,4})?',  # Date format (e.g., 12/25, 12/25/2023)
            r'\d{1,2}-\d{1,2}(-\d{2,4})?',  # Date format (e.g., 12-25, 12-25-2023)
            r'(next|last|this)\s+(week|month|year|monday|tuesday|wednesday|thursday|friday|saturday|sunday)',
            r'(tomorrow|today|yesterday)',
            r'in\s+\d+\s+(minutes?|hours?|days?|weeks?|months?)',
            r'\d+\s+(minutes?|hours?|days?|weeks?|months?)\s+(ago|from now)'
        ]
        
        count = 0
        for pattern in time_patterns:
            count += len(re.findall(pattern, message, re.IGNORECASE))
        
        return count
    
    def _get_irrelevant_response(self) -> str:
        """Get response for irrelevant messages."""
        return """I'm Zeitwahl, your AI scheduling and time management assistant. 

I specialize in helping with:
• Calendar management and scheduling
• Time zone conversions  
• Meeting planning and coordination
• Productivity and time management advice
• Date and time calculations

Your message doesn't seem to be related to scheduling or time management. How can I help you with your calendar or time-related needs today?"""
    
    async def validate_user_registration_input(self, message_text: str) -> Dict[str, Any]:
        """Validate user input during registration process."""
        # Check for timezone patterns
        timezone_patterns = [
            r'UTC[+-]?\d{0,2}',
            r'GMT[+-]?\d{0,2}',
            r'[A-Z]{3,4}',  # Common timezone abbreviations
            r'[A-Za-z_]+/[A-Za-z_]+',  # IANA timezone format
        ]
        
        for pattern in timezone_patterns:
            if re.search(pattern, message_text, re.IGNORECASE):
                return {
                    "is_valid_timezone": True,
                    "extracted_timezone": re.search(pattern, message_text, re.IGNORECASE).group(),
                    "needs_confirmation": True
                }
        
        return {
            "is_valid_timezone": False,
            "extracted_timezone": None,
            "needs_clarification": True
        }
