"""
Preprocessing Actor for handling message preprocessing and context building.

This actor receives messages from <PERSON>t<PERSON><PERSON>, identifies users, validates messages,
grabs context, and builds comprehensive prompts for LLM processing.
"""

import logging
from typing import Dict, Any, Optional

from app.utils.actor import Actor
from app.utils.event_bus import event_bus
from app.services.context_grabber_service import ContextGrabberService
from app.services.prompt_builder_service import PromptBuilderService
from app.services.validation_service import ValidationService
from app.utils.events import (
    UserMessageReceived, UserIdentified, ContextBuilt, 
    LLMRequestReady, GenericResponse, ErrorOccurred
)

logger = logging.getLogger(__name__)


class PreprocessingActor(Actor):
    """Actor responsible for message preprocessing and context building."""
    
    def __init__(self, name: Optional[str] = None):
        super().__init__(name or "PreprocessingActor")
        self.context_grabber = None
        self.prompt_builder = None
        self.validation_service = None
    
    async def initialize(self) -> None:
        """Initialize the preprocessing actor."""
        try:
            logger.info(f"Initializing {self.name}")
            
            # Initialize services
            self.context_grabber = ContextGrabberService()
            self.prompt_builder = PromptBuilderService()
            self.validation_service = ValidationService()
            
            logger.info(f"{self.name} initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize {self.name}: {e}")
            raise
    
    async def start_actor(self) -> None:
        """Start the preprocessing actor."""
        logger.info(f"{self.name} started and ready to process messages")
    
    async def stop_actor(self) -> None:
        """Stop the preprocessing actor."""
        logger.info(f"{self.name} stopped successfully")
    
    @event_bus.subscribe("user_message_received")
    async def handle_user_message(self, event_data: UserMessageReceived) -> None:
        """Handle incoming user messages."""
        try:
            logger.debug(f"Processing message from user {event_data.telegram_user_id}")
            
            # First, request user identification from DBActor
            await event_bus.publish("user_identification_request", {
                "telegram_user_id": event_data.telegram_user_id,
                "chat_id": event_data.chat_id,
                "telegram_message_id": event_data.telegram_message_id,
                "user_info": {
                    "username": event_data.username,
                    "first_name": event_data.first_name,
                    "last_name": event_data.last_name
                },
                "timestamp": event_data.timestamp
            })
            
            # Store the message for later processing
            await event_bus.publish("store_message_request", {
                "telegram_message_id": event_data.telegram_message_id,
                "chat_id": event_data.chat_id,
                "telegram_user_id": event_data.telegram_user_id,
                "message_text": event_data.message_text,
                "message_type": event_data.message_type,
                "is_from_bot": False,
                "telegram_timestamp": event_data.telegram_timestamp,
                "is_reply": event_data.is_reply,
                "reply_to_message_id": event_data.reply_to_message_id,
                "chat_type": event_data.chat_type,
                "chat_title": event_data.chat_title
            })
            
        except Exception as e:
            logger.error(f"Error handling user message: {e}")
            await self._publish_error("handle_user_message", str(e), event_data.__dict__)
    
    @event_bus.subscribe("user_identified")
    async def handle_user_identified(self, event_data: UserIdentified) -> None:
        """Handle user identification results."""
        try:
            if event_data.needs_registration:
                # New user needs registration
                logger.info(f"New user {event_data.telegram_user_id} needs registration")
                
                # Send registration prompt
                await event_bus.publish("generic_response", GenericResponse(
                    chat_id=event_data.chat_id,
                    message_type="new_user",
                    reply_to_message_id=event_data.telegram_message_id
                ))
                
                return
            
            # Existing user, continue with message processing
            logger.debug(f"User {event_data.telegram_user_id} identified, continuing processing")
            
            # Get the original message (we need to retrieve it)
            # For now, we'll trigger context building - in a real implementation,
            # we'd store the original message and retrieve it here
            await self._continue_message_processing(event_data)
            
        except Exception as e:
            logger.error(f"Error handling user identification: {e}")
            await self._publish_error("handle_user_identified", str(e), event_data.__dict__)
    
    async def _continue_message_processing(self, user_data: UserIdentified) -> None:
        """Continue processing after user identification."""
        try:
            # We need to get the original message text
            # In a real implementation, we'd store this during the initial message handling
            # For now, we'll request it from the database
            
            # Request conversation history to get the current message
            await event_bus.publish("get_conversation_history_request", {
                "chat_id": user_data.chat_id,
                "telegram_user_id": user_data.telegram_user_id,
                "limit": 1,
                "processing_context": {
                    "telegram_message_id": user_data.telegram_message_id,
                    "user_data": user_data.user_data
                }
            })
            
        except Exception as e:
            logger.error(f"Error continuing message processing: {e}")
            await self._publish_error("continue_message_processing", str(e), user_data.__dict__)
    
    @event_bus.subscribe("conversation_history_response")
    async def handle_conversation_history(self, event_data: Dict[str, Any]) -> None:
        """Handle conversation history response for message processing."""
        try:
            processing_context = event_data.get("processing_context")
            if not processing_context:
                return  # Not for us
            
            history = event_data.get("history", [])
            if not history:
                logger.warning("No message found in conversation history")
                return
            
            # Get the current message (should be the first in history)
            current_message = history[0]
            message_text = current_message.get("message_text", "")
            
            # Validate the message
            validation_result = await self.validation_service.validate_and_classify_message(message_text)
            
            if not validation_result.get("needs_llm", True):
                # Message doesn't need LLM processing, send suggested response
                suggested_response = validation_result.get("suggested_response")
                if suggested_response:
                    await event_bus.publish("send_message_request", {
                        "chat_id": event_data.get("chat_id"),
                        "message_text": suggested_response,
                        "reply_to_message_id": processing_context.get("telegram_message_id")
                    })
                return
            
            # Message needs LLM processing, build context and prompt
            await self._build_context_and_prompt(
                event_data.get("chat_id"),
                event_data.get("telegram_user_id"),
                processing_context.get("telegram_message_id"),
                message_text,
                processing_context.get("user_data"),
                history[1:]  # Exclude current message from history
            )
            
        except Exception as e:
            logger.error(f"Error handling conversation history: {e}")
            await self._publish_error("handle_conversation_history", str(e), event_data)
    
    async def _build_context_and_prompt(
        self,
        chat_id: int,
        telegram_user_id: int,
        telegram_message_id: int,
        message_text: str,
        user_data: Dict[str, Any],
        conversation_history: list
    ) -> None:
        """Build context and prompt for LLM processing."""
        try:
            # Get comprehensive context
            context = await self.context_grabber.get_user_context(
                telegram_user_id, chat_id, message_text
            )
            
            # Build the prompt
            prompt = await self.prompt_builder.build_prompt(
                message_text=message_text,
                context=context,
                conversation_history=conversation_history,
                user_data=user_data
            )
            
            # Publish context built event
            await event_bus.publish("context_built", ContextBuilt(
                telegram_user_id=telegram_user_id,
                chat_id=chat_id,
                telegram_message_id=telegram_message_id,
                original_message=message_text,
                context=context,
                conversation_history=conversation_history,
                prompt=prompt,
                is_relevant=True
            ))
            
            # Publish LLM request ready event
            await event_bus.publish("llm_request_ready", LLMRequestReady(
                telegram_user_id=telegram_user_id,
                chat_id=chat_id,
                telegram_message_id=telegram_message_id,
                prompt=prompt,
                context=context
            ))
            
        except Exception as e:
            logger.error(f"Error building context and prompt: {e}")
            await self._publish_error("build_context_and_prompt", str(e), {
                "chat_id": chat_id,
                "telegram_user_id": telegram_user_id,
                "telegram_message_id": telegram_message_id
            })
    
    async def _publish_error(self, operation: str, error_message: str, original_event: Dict[str, Any]) -> None:
        """Publish an error event."""
        await event_bus.publish("error_occurred", ErrorOccurred(
            user_id=original_event.get("telegram_user_id"),
            chat_id=original_event.get("chat_id"),
            message_id=original_event.get("telegram_message_id"),
            error_type="preprocessing_error",
            error_message=error_message,
            component=f"{self.name}.{operation}"
        ))
